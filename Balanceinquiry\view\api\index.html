<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>余额查询</title>
    <link rel="stylesheet" href="/static/others/element-plus/index.css">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
    <style>
        body {
            margin: 0;
            background: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            height: 100vh;
            overflow: hidden;
        }

        .main-container {
            padding: 20px;
            height: calc(100vh - 40px);
            box-sizing: border-box;
        }

        .balance-card {
            background: #fff;
            border-radius: 8px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .el-table {
            --el-table-row-height: 55px;
            font-size: 14px;
            height: 100%;
        }

        .el-table th {
            background-color: #f5f7fa !important;
            font-weight: bold;
            color: #606266;
            font-size: 15px;
        }

        .el-table td {
            padding: 8px 0;
        }

        .username-cell {
            font-weight: 500;
            color: #303133;
        }

        .balance-value {
            color: #67c23a;
            font-weight: 600;
            margin-left: 12px;
        }

        .filter-container {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        }

        .table-card {
            margin-top: 20px;
        }

        .user-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .total-balance {
            text-align: right;
            background: #f0f9eb;
            padding: 10px 20px;
            border-radius: 6px;
            border: 1px solid #e1f3d8;
            display: inline-block;
            float: right;
        }
        
        .total-balance-label {
            font-size: 14px;
            color: #606266;
            margin-right: 8px;
        }
        
        .total-balance-value {
            font-size: 16px;
            color: #67c23a;
            font-weight: 600;
        }

        .refresh-button {
            margin-top: 20px;
            width: 100%;
        }

        /* 美化滚动条 */
        .el-table__body-wrapper::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        .el-table__body-wrapper::-webkit-scrollbar-thumb {
            border-radius: 3px;
            background: #dcdfe6;
        }

        .el-table__body-wrapper::-webkit-scrollbar-track {
            border-radius: 3px;
            background: #f5f7fa;
        }

        /* 资金明细弹窗样式 */
        .el-dialog {
            border-radius: 8px;
        }
        
        .el-dialog__header {
            margin: 0;
            padding: 20px 24px;
            border-bottom: 1px solid #dcdfe6;
        }
        
        .el-dialog__title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
        }
        
        .el-dialog__body {
            padding: 24px;
        }
        
        .el-dialog__footer {
            padding: 16px 24px;
            border-top: 1px solid #dcdfe6;
        }
        
        /* 表格样式优化 */
        .el-table {
            --el-table-border-color: #ebeef5;
        }
        
        .el-table th.el-table__cell {
            background-color: #f5f7fa !important;
            padding: 12px 0;
        }
        
        .el-table td.el-table__cell {
            padding: 12px 0;
        }
        
        /* 标签样式 */
        .el-tag--info {
            background-color: #f4f4f5;
            border-color: #e9e9eb;
            color: #909399;
        }

        /* 添加搜索框样式 */
        .el-input-group__append button {
            padding: 0 15px;
        }

        /* 弹窗样式优化 */
        .money-log-dialog .el-dialog__body {
            padding: 20px;
            height: calc(100vh - 180px);
            overflow: hidden;
        }

        /* 表格容器样式 */
        .table-container {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        /* 分页器样式 */
        .pagination-container {
            margin-top: 20px;
            padding: 10px 0;
            flex-shrink: 0;
            display: flex;
            justify-content: flex-end;
        }

        /* 表格自适应 */
        .el-table {
            flex: 1;
            height: calc(100% - 120px) !important;
        }

        /* 确保弹窗内容不会溢出 */
        .el-dialog__body {
            overflow: hidden;
        }

        /* 优化表格在小屏幕上的显示 */
        @media screen and (max-width: 1200px) {
            .el-table .el-table__cell {
                padding: 8px 0;
            }
            
            .el-table {
                font-size: 13px;
            }
        }

        /* 响应式设计 */
        /* 平板设备 (768px - 1023px) */
        @media screen and (min-width: 768px) and (max-width: 1023px) {
            .filter-container {
                padding: 16px;
            }
            
            .total-balance {
                position: static;
                margin-top: 16px;
                text-align: right;
            }
            
            .el-col {
                margin-bottom: 10px;
            }
            
            .main-container {
                height: auto;
                padding: 10px;
            }
            
            .el-table {
                font-size: 13px;
            }
        }

        /* 手机设备 (最大 767px) */
        @media screen and (max-width: 767px) {
            .filter-container {
                padding: 12px;
            }
            
            .total-balance {
                position: static;
                margin-top: 12px;
                width: 100%;
                text-align: center;
            }
            
            .el-col {
                width: 100% !important;
                margin-bottom: 10px;
            }
            
            .main-container {
                height: auto;
                padding: 8px;
            }
            
            .balance-card {
                margin: 0;
            }
            
            .el-table {
                font-size: 12px;
            }
            
            .username-cell {
                display: block;
                margin-bottom: 4px;
            }
            
            .balance-value {
                margin-left: 0;
            }
            
            /* 弹窗样式调整 */
            .el-dialog {
                width: 95% !important;
                margin: 10px auto !important;
            }
            
            .money-log-dialog .el-dialog__body {
                padding: 12px;
                height: calc(90vh - 120px);
            }
            
            .pagination-container {
                flex-direction: column;
                align-items: center;
            }
            
            .el-pagination {
                padding: 0;
                justify-content: center;
            }
        }

        /* 响应式调整 */
        @media screen and (max-height: 768px) {
            .money-log-dialog .el-dialog__body {
                height: calc(100vh - 140px);
            }
            
            .el-table {
                height: calc(100% - 100px) !important;
            }
            
            .search-container {
                margin-bottom: 15px;
            }
            
            .pagination-container {
                margin-top: 15px;
                padding: 5px 0;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 错误提示 -->
        <el-result
            v-if="error"
            :icon="error.icon || 'Error'"
            :title="error.message"
            :sub-title="error.subTitle">
        </el-result>

        <!-- 主内容区域 -->
        <div v-else class="main-container">
            <div class="balance-card">
                <!-- 搜索区域 -->
                <div class="filter-container">
                    <el-row :gutter="20" style="align-items: center;">
                        <el-col :span="8">
                            <el-input
                                v-model="searchKey"
                                placeholder="搜索用户名"
                                clearable
                                @keyup.enter="handleSearch"
                                @clear="handleSearch">
                                <template #append>
                                    <el-button @click="handleSearch">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" style="fill: currentColor;">
                                            <path d="M10.5 3a7.5 7.5 0 015.645 12.438l4.709 4.708a.502.502 0 01-.708.708l-4.708-4.709A7.5 7.5 0 1110.5 3zm0 1a6.5 6.5 0 100 13 6.5 6.5 0 000-13z"/>
                                        </svg>
                                    </el-button>
                                </template>
                            </el-input>
                        </el-col>
                        <el-col :span="4">
                            <el-button type="primary" @click="handleReset">重置</el-button>
                        </el-col>
                        <el-col :span="4">
                            <el-button type="danger" @click="showClearAllDialog">批量清除</el-button>
                        </el-col>
                        <el-col :span="8">
                            <div class="total-balance">
                                <span class="total-balance-label">总余额:</span>
                                <span class="total-balance-value">¥{{ totalBalance }}</span>
                            </div>
                        </el-col>
                    </el-row>
                </div>

                <el-card v-loading="loading" class="table-card">
                    <el-table 
                        :data="userList" 
                        style="width: 100%"
                        :header-cell-style="{
                            background: '#f5f7fa',
                            color: '#606266',
                            fontWeight: 'bold'
                        }">
                        <el-table-column 
                            label="用户信息" 
                            align="center"
                            min-width="200">
                            <template #default="scope">
                                <div class="user-info">
                                    <span class="username-cell">{{ scope.row.username }}</span>
                                    <span class="balance-value">¥{{ scope.row.balance }}</span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="操作"
                            align="center"
                            width="200">
                            <template #default="scope">
                                <el-button
                                    type="primary"
                                    link
                                    @click="showMoneyLog(scope.row)">
                                    资金明细
                                </el-button>
                                <el-button
                                    type="danger"
                                    link
                                    @click="showClearDialog(scope.row)"
                                    :disabled="parseFloat(scope.row.balance) === 0">
                                    清除余额
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页器 -->
                    <div class="pagination-container">
                        <el-pagination
                            v-model:current-page="balanceCurrentPage"
                            v-model:page-size="balancePageSize"
                            :page-sizes="[10, 20, 50, 100]"
                            :total="balanceTotal"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleBalanceSizeChange"
                            @current-change="handleBalanceCurrentChange">
                        </el-pagination>
                    </div>

                    <el-button 
                        class="refresh-button"
                        type="primary"
                        :loading="loading"
                        @click="getBalance">
                        <template #icon>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" style="margin-right: 4px;">
                                <path fill="currentColor" d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
                            </svg>
                        </template>
                        刷新余额
                    </el-button>
                </el-card>
            </div>

            <!-- 资金明细弹窗 -->
            <el-dialog
                v-model="moneyLogVisible"
                title="资金明细"
                width="80%"
                :close-on-click-modal="false"
                class="money-log-dialog">
                
                <div class="table-container">
                    <!-- 搜索框 -->
                    <div class="search-container">
                        <el-input
                            v-model="moneyLogSearch"
                            placeholder="搜索说明内容"
                            clearable
                            style="width: 300px"
                            @keyup.enter="searchMoneyLog"
                            @clear="searchMoneyLog">
                            <template #append>
                                <el-button @click="searchMoneyLog">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" style="fill: currentColor;">
                                        <path d="M10.5 3a7.5 7.5 0 015.645 12.438l4.709 4.708a.502.502 0 01-.708.708l-4.708-4.709A7.5 7.5 0 1110.5 3zm0 1a6.5 6.5 0 100 13 6.5 6.5 0 000-13z"/>
                                    </svg>
                                </el-button>
                            </template>
                        </el-input>
                    </div>

                    <!-- 表格 -->
                    <el-table
                        v-loading="moneyLogLoading"
                        :data="moneyLogs"
                        style="width: 100%"
                        :header-cell-style="{
                            background: '#f5f7fa',
                            color: '#606266',
                            fontWeight: 'bold',
                            fontSize: '15px'
                        }"
                        border>
                        <el-table-column
                            prop="create_time"
                            label="时间"
                            min-width="180"
                            align="center">
                            <template #default="scope">
                                <span style="color: #606266">{{ scope.row.create_time }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="change"
                            label="变动金额"
                            min-width="120"
                            align="center">
                            <template #default="scope">
                                <span :style="{
                                    color: parseFloat(scope.row.change) >= 0 ? '#67c23a' : '#f56c6c',
                                    fontWeight: '600'
                                }">
                                    {{ parseFloat(scope.row.change) >= 0 ? '+' : '' }}{{ scope.row.change }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="reason"
                            label="说明"
                            min-width="300"
                            align="center">
                            <template #default="scope">
                                <span style="color: #303133">{{ scope.row.reason }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="source"
                            label="来源"
                            min-width="100"
                            align="center">
                            <template #default="scope">
                                <el-tag size="small" type="info">
                                    {{ scope.row.source === 'Platform' ? '平台钱包' : scope.row.source }}
                                </el-tag>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页器 -->
                    <div class="pagination-container">
                        <el-pagination
                            v-model:current-page="currentPage"
                            v-model:page-size="pageSize"
                            :page-sizes="[10, 20, 50, 100]"
                            :total="moneyLogTotal"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange">
                        </el-pagination>
                    </div>
                </div>
            </el-dialog>

            <!-- 单个用户余额清除确认对话框 -->
            <el-dialog
                v-model="clearDialogVisible"
                title="确认清除余额"
                width="400px"
                :close-on-click-modal="false">
                <div style="text-align: center; padding: 20px 0;">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="48" height="48" style="margin-bottom: 16px; color: #f56c6c;">
                        <path fill="currentColor" d="M12 2L1 21h22L12 2zm0 3.99L19.53 19H4.47L12 5.99zM11 16v2h2v-2h-2zm0-6v4h2v-4h-2z"/>
                    </svg>
                    <p style="font-size: 16px; margin-bottom: 8px;">确定要清除用户余额吗？</p>
                    <p style="color: #606266; margin-bottom: 16px;">
                        用户：<strong>{{ currentClearUser?.username }}</strong><br>
                        当前余额：<strong style="color: #f56c6c;">¥{{ currentClearUser?.balance }}</strong>
                    </p>
                    <p style="color: #f56c6c; font-size: 14px;">此操作不可撤销，请谨慎操作！</p>
                </div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="clearDialogVisible = false">取消</el-button>
                        <el-button
                            type="danger"
                            @click="confirmClearBalance"
                            :loading="clearLoading">
                            确认清除
                        </el-button>
                    </span>
                </template>
            </el-dialog>

            <!-- 批量清除余额确认对话框 -->
            <el-dialog
                v-model="clearAllDialogVisible"
                title="确认批量清除所有余额"
                width="500px"
                :close-on-click-modal="false">
                <div style="text-align: center; padding: 20px 0;">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="48" height="48" style="margin-bottom: 16px; color: #f56c6c;">
                        <path fill="currentColor" d="M12 2L1 21h22L12 2zm0 3.99L19.53 19H4.47L12 5.99zM11 16v2h2v-2h-2zm0-6v4h2v-4h-2z"/>
                    </svg>
                    <p style="font-size: 16px; margin-bottom: 16px; color: #f56c6c; font-weight: bold;">
                        ⚠️ 危险操作警告 ⚠️
                    </p>
                    <p style="color: #606266; margin-bottom: 16px;">
                        您即将清除<strong>所有用户</strong>的余额！<br>
                        当前系统总余额：<strong style="color: #f56c6c;">¥{{ totalBalance }}</strong>
                    </p>
                    <p style="color: #f56c6c; font-size: 14px; margin-bottom: 20px;">
                        此操作将无法撤销，请确保您有足够的权限执行此操作！
                    </p>

                    <el-input
                        v-model="confirmCode"
                        placeholder="请输入确认码：CLEAR_ALL_BALANCE_CONFIRM"
                        style="margin-bottom: 16px;"
                        @keyup.enter="confirmClearAllBalance">
                    </el-input>

                    <p style="color: #909399; font-size: 12px;">
                        请输入确认码以继续操作
                    </p>
                </div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="clearAllDialogVisible = false">取消</el-button>
                        <el-button
                            type="danger"
                            @click="confirmClearAllBalance"
                            :loading="clearAllLoading"
                            :disabled="confirmCode !== 'CLEAR_ALL_BALANCE_CONFIRM'">
                            确认批量清除
                        </el-button>
                    </span>
                </template>
            </el-dialog>
        </div>
    </div>

    <script src="/static/others/vue/vue.global.prod.js"></script>
    <script src="/static/others/element-plus/index.full.min.js"></script>
    <script src="/static/others/axios/axios.min.js"></script>
    <script src="/static/others/element-plus/locale/zh-cn.min.js"></script>

    <script>
        const { createApp, ref, onMounted, computed } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                const loading = ref(false);
                const userList = ref([]);
                const error = ref(null);
                const searchKey = ref('');
                const totalBalance = ref('0.00');

                const moneyLogVisible = ref(false);
                const moneyLogLoading = ref(false);
                const moneyLogs = ref([]);
                const currentUser = ref(null);
                const moneyLogSearch = ref('');
                const currentPage = ref(1);
                const pageSize = ref(20);
                const moneyLogTotal = ref(0);

                // 添加分页相关的响应式变量
                const balanceCurrentPage = ref(1);
                const balancePageSize = ref(10);
                const balanceTotal = ref(0);

                // 余额清除相关的响应式变量
                const clearDialogVisible = ref(false);
                const clearAllDialogVisible = ref(false);
                const currentClearUser = ref(null);
                const clearLoading = ref(false);
                const clearAllLoading = ref(false);
                const confirmCode = ref('');

                const getBalance = async () => {
                    if (loading.value) return;
                    
                    loading.value = true;
                    try {
                        const res = await axios.get('/plugin/Balanceinquiry/api/getBalance', {
                            params: {
                                search: searchKey.value,
                                page: balanceCurrentPage.value,
                                limit: balancePageSize.value
                            }
                        });
                        if (res.data.code === 200) {
                            userList.value = res.data.data;
                            totalBalance.value = res.data.totalBalance;
                            balanceTotal.value = res.data.total;
                            error.value = null;
                        } else {
                            error.value = {
                                icon: 'Error',
                                message: res.data.msg || '获取失败'
                            };
                        }
                    } catch (err) {
                        error.value = {
                            icon: 'Error',
                            message: '获取失败',
                            subTitle: '请稍后重试'
                        };
                        console.error('获取余额失败:', err);
                    } finally {
                        loading.value = false;
                    }
                };

                const handleSearch = () => {
                    getBalance();
                };

                const handleReset = () => {
                    searchKey.value = '';
                    getBalance();
                };

                const loadMoneyLog = async (user, search = '') => {
                    moneyLogLoading.value = true;
                    try {
                        const res = await axios.get('/plugin/Balanceinquiry/api/getMoneyLog', {
                            params: {
                                username: user.username,
                                search: search,
                                page: currentPage.value,
                                limit: pageSize.value
                            }
                        });

                        if (res.data.code === 200) {
                            moneyLogs.value = res.data.data;
                            moneyLogTotal.value = res.data.total;
                        } else {
                            ElMessage.error(res.data.msg || '获取资金明细失败');
                            if (res.data.code === 404 && search) {
                                moneyLogs.value = [];
                                moneyLogTotal.value = 0;
                            } else {
                                moneyLogVisible.value = false;
                            }
                        }
                    } catch (err) {
                        console.error('获取资金明细失败:', err);
                        ElMessage.error('获取资金明细失败');
                        moneyLogVisible.value = false;
                    } finally {
                        moneyLogLoading.value = false;
                    }
                };

                const showMoneyLog = async (user) => {
                    moneyLogVisible.value = true;
                    moneyLogSearch.value = '';
                    currentUser.value = user;
                    currentPage.value = 1; // 重置页码
                    await loadMoneyLog(user);
                };

                const searchMoneyLog = async () => {
                    if (!currentUser.value) return;
                    await loadMoneyLog(currentUser.value, moneyLogSearch.value);
                };

                // 资金明细分页处理方法
                const handleSizeChange = (val) => {
                    pageSize.value = val;
                    currentPage.value = 1;
                    if (currentUser.value) {
                        loadMoneyLog(currentUser.value, moneyLogSearch.value);
                    }
                };

                const handleCurrentChange = (val) => {
                    currentPage.value = val;
                    if (currentUser.value) {
                        loadMoneyLog(currentUser.value, moneyLogSearch.value);
                    }
                };

                // 余额列表分页处理
                const handleBalanceSizeChange = (val) => {
                    balancePageSize.value = val;
                    balanceCurrentPage.value = 1;
                    getBalance();
                };

                const handleBalanceCurrentChange = (val) => {
                    balanceCurrentPage.value = val;
                    getBalance();
                };

                // 显示单个用户余额清除确认对话框
                const showClearDialog = (user) => {
                    currentClearUser.value = user;
                    clearDialogVisible.value = true;
                };

                // 显示批量清除余额确认对话框
                const showClearAllDialog = () => {
                    confirmCode.value = '';
                    clearAllDialogVisible.value = true;
                };

                // 确认清除单个用户余额
                const confirmClearBalance = async () => {
                    if (!currentClearUser.value) return;

                    clearLoading.value = true;
                    try {
                        const res = await axios.post('/plugin/Balanceinquiry/api/clearBalance', {
                            username: currentClearUser.value.username
                        });

                        if (res.data.code === 200) {
                            ElMessage.success(res.data.msg);
                            clearDialogVisible.value = false;
                            currentClearUser.value = null;
                            // 刷新余额列表
                            await getBalance();
                        } else {
                            ElMessage.error(res.data.msg || '清除失败');
                        }
                    } catch (err) {
                        console.error('清除余额失败:', err);
                        ElMessage.error('清除失败，请稍后重试');
                    } finally {
                        clearLoading.value = false;
                    }
                };

                // 确认批量清除所有用户余额
                const confirmClearAllBalance = async () => {
                    if (confirmCode.value !== 'CLEAR_ALL_BALANCE_CONFIRM') {
                        ElMessage.error('确认码错误');
                        return;
                    }

                    clearAllLoading.value = true;
                    try {
                        const res = await axios.post('/plugin/Balanceinquiry/api/clearAllBalance', {
                            confirm_code: confirmCode.value
                        });

                        if (res.data.code === 200) {
                            ElMessage.success(res.data.msg);
                            clearAllDialogVisible.value = false;
                            confirmCode.value = '';
                            // 刷新余额列表
                            await getBalance();
                        } else {
                            ElMessage.error(res.data.msg || '批量清除失败');
                        }
                    } catch (err) {
                        console.error('批量清除余额失败:', err);
                        ElMessage.error('批量清除失败，请稍后重试');
                    } finally {
                        clearAllLoading.value = false;
                    }
                };

                onMounted(() => {
                    getBalance();
                });

                return {
                    loading,
                    userList,
                    getBalance,
                    error,
                    searchKey,
                    totalBalance,
                    handleSearch,
                    handleReset,
                    moneyLogVisible,
                    moneyLogLoading,
                    moneyLogs,
                    showMoneyLog,
                    moneyLogSearch,
                    searchMoneyLog,
                    currentPage,
                    pageSize,
                    handleSizeChange,
                    handleCurrentChange,
                    balanceCurrentPage,
                    balancePageSize,
                    balanceTotal,
                    handleBalanceSizeChange,
                    handleBalanceCurrentChange,
                    moneyLogTotal,
                    // 余额清除相关
                    clearDialogVisible,
                    clearAllDialogVisible,
                    currentClearUser,
                    clearLoading,
                    clearAllLoading,
                    confirmCode,
                    showClearDialog,
                    showClearAllDialog,
                    confirmClearBalance,
                    confirmClearAllBalance
                };
            }
        }).use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        }).mount('#app');
    </script>
</body>
</html> 